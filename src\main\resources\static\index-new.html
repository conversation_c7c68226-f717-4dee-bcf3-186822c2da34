<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纳税人信息查询系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style-new.css" rel="stylesheet">
</head>
<body class="eye-protection">
    <div x-data="taxpayerApp()" x-init="init()">
        <!-- 页面标题 -->
        <header class="container mx-auto py-6">
            <div class="flex items-center justify-between">
                <!-- 左侧：图标和标题 -->
                <div class="flex items-center gap-4">
                    <div class="inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg float-animation">
                        <i class="fas fa-building text-xl text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold gradient-text">
                            纳税人信息查询系统
                        </h1>
                        <p class="text-slate-500 text-sm mt-1">基于现代化架构的智能纳税人信息管理平台</p>
                    </div>
                </div>

                <!-- 右侧：特性标签 -->
                <div class="hidden md:flex items-center gap-2">
                    <span class="feature-tag px-3 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">高效</span>
                    <span class="feature-tag px-3 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">安全</span>
                    <span class="feature-tag px-3 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">智能</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="container mx-auto px-4">
            
            <!-- 查询表单区域 -->
            <section class="glass-card p-4 mb-6 fade-in">
                <div class="flex flex-col lg:flex-row lg:items-end gap-4">
                    <!-- 关键词搜索 -->
                    <div class="flex-1">
                        <input
                            type="text"
                            x-model="searchForm.keyword"
                            @keyup.enter="queryData()"
                            placeholder="🔍 输入纳税人名称、识别号、法人姓名等关键词进行搜索..."
                            class="input-field text-base h-12"
                        >
                    </div>

                    <!-- 精确查询字段 -->
                    <div class="flex gap-3">
                        <input
                            type="text"
                            x-model="searchForm.nsrsbh"
                            placeholder="纳税人识别号"
                            class="input-field w-40 h-12"
                        >
                        <input
                            type="text"
                            x-model="searchForm.nsrmc"
                            placeholder="纳税人名称"
                            class="input-field w-40 h-12"
                        >
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-2">
                        <button
                            @click="queryData()"
                            :disabled="loading"
                            class="btn-primary h-12 px-6"
                        >
                            <i class="fas fa-search mr-2"></i>
                            <span x-show="!loading">查询</span>
                            <span x-show="loading" class="flex items-center">
                                <span class="loading-spinner mr-2"></span>查询中...
                            </span>
                        </button>

                        <button
                            @click="resetForm()"
                            class="btn-secondary h-12 px-4"
                        >
                            <i class="fas fa-redo"></i>
                        </button>

                        <button
                            @click="exportData()"
                            class="btn-success h-12 px-4"
                        >
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 数据表格区域 -->
            <section class="table-container fade-in">
                <div class="table-header">
                    <div class="flex justify-between items-center">
                        <h3 class="font-semibold">
                            <i class="fas fa-table mr-2"></i>查询结果
                        </h3>
                        <span class="text-sm text-slate-600" x-show="pageData.total > 0">
                            共 <span x-text="pageData.total"></span> 条记录
                        </span>
                    </div>
                </div>
                
                <!-- 表格内容 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-slate-50 to-slate-100">
                            <tr>
                                <th class="table-cell font-semibold text-left text-slate-700">序号</th>
                                <th class="table-cell font-semibold text-left text-slate-700">纳税人识别号</th>
                                <th class="table-cell font-semibold text-left text-slate-700">纳税人名称</th>
                                <th class="table-cell font-semibold text-left text-slate-700">纳税人性质</th>
                                <th class="table-cell font-semibold text-left text-slate-700">税务机关</th>
                                <th class="table-cell font-semibold text-left text-slate-700">法定代表人</th>
                                <th class="table-cell font-semibold text-left text-slate-700">联系电话</th>
                                <th class="table-cell font-semibold text-left text-slate-700">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="(item, index) in pageData.records" :key="item.nsrsbh">
                                <tr class="table-row">
                                    <td class="table-cell text-slate-600 font-medium" x-text="(pageData.current - 1) * pageData.size + index + 1"></td>
                                    <td class="table-cell">
                                        <span class="font-mono text-sm bg-slate-100 px-2 py-1 rounded text-slate-700" x-text="item.nsrsbh"></span>
                                    </td>
                                    <td class="table-cell">
                                        <div class="font-medium text-slate-800" x-text="item.nsrmc"></div>
                                    </td>
                                    <td class="table-cell">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200" x-text="item.nsrxz || '-'"></span>
                                    </td>
                                    <td class="table-cell">
                                        <div class="text-sm text-slate-600 max-w-xs truncate" :title="item.swjgMc" x-text="item.swjgMc || '-'"></div>
                                    </td>
                                    <td class="table-cell">
                                        <div class="text-slate-700 font-medium" x-text="item.fddbrxm || '-'"></div>
                                    </td>
                                    <td class="table-cell">
                                        <div class="text-sm text-slate-600" x-text="item.fddbrdhhm || '-'"></div>
                                    </td>
                                    <td class="table-cell">
                                        <button
                                            @click="viewDetail(item.nsrsbh)"
                                            class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs font-medium rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
                                        >
                                            <i class="fas fa-eye mr-1"></i>详情
                                        </button>
                                    </td>
                                </tr>
                            </template>
                            
                            <!-- 无数据提示 -->
                            <tr x-show="!loading && pageData.records.length === 0">
                                <td colspan="8" class="table-cell text-center text-slate-500 py-12">
                                    <div class="flex flex-col items-center">
                                        <div class="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                                            <i class="fas fa-search text-2xl text-slate-400"></i>
                                        </div>
                                        <p class="text-lg font-medium text-slate-600 mb-2">暂无查询结果</p>
                                        <p class="text-sm text-slate-500">请调整查询条件后重试</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="pagination-container" x-show="pageData.total > 0">
                    <button 
                        @click="changePage(1)"
                        :disabled="pageData.current <= 1"
                        class="pagination-btn"
                    >
                        首页
                    </button>
                    <button 
                        @click="changePage(pageData.current - 1)"
                        :disabled="pageData.current <= 1"
                        class="pagination-btn"
                    >
                        上一页
                    </button>
                    
                    <span class="pagination-current" x-text="pageData.current"></span>
                    
                    <button 
                        @click="changePage(pageData.current + 1)"
                        :disabled="pageData.current >= pageData.pages"
                        class="pagination-btn"
                    >
                        下一页
                    </button>
                    <button 
                        @click="changePage(pageData.pages)"
                        :disabled="pageData.current >= pageData.pages"
                        class="pagination-btn"
                    >
                        末页
                    </button>
                    
                    <div class="flex items-center gap-2 ml-4 text-sm text-slate-600">
                        <span>第 <span x-text="pageData.current"></span> 页，共 <span x-text="pageData.pages"></span> 页</span>
                        <span class="mx-2">|</span>
                        <span>跳转到</span>
                        <input
                            type="number"
                            x-model="jumpToPage"
                            @keyup.enter="jumpToPageNumber()"
                            :min="1"
                            :max="pageData.pages"
                            class="w-16 px-2 py-1 text-center border border-slate-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="页码"
                        >
                        <span>页</span>
                        <button
                            @click="jumpToPageNumber()"
                            class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                        >
                            跳转
                        </button>
                    </div>
                </div>
            </section>
        </main>
        
        <!-- 页脚 -->
        <footer class="container mx-auto py-8 text-center">
            <p class="text-slate-500 text-sm">
                © 2024 纳税人信息查询系统 - 专业、高效、安全
            </p>
        </footer>
    </div>

    <!-- JavaScript应用逻辑 -->
    <script src="js/app-new.js"></script>
</body>
</html>
