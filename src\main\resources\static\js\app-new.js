/**
 * 纳税人信息查询系统前端应用 - 护眼版本
 */

// 缓存键名常量
const CACHE_KEYS = {
    SEARCH_FORM: 'taxpayer_search_form',
    PAGE_DATA: 'taxpayer_page_data',
    CURRENT_PAGE: 'taxpayer_current_page',
    PAGE_SIZE: 'taxpayer_page_size'
};

// API基础路径
const API_BASE = '/api/taxpayer';

// 纳税人查询应用
function taxpayerApp() {
    return {
        // 数据状态
        loading: false,
        searchForm: {
            keyword: '',
            nsrsbh: '',
            nsrmc: '',
            nsrxz: '',
            swjgMc: '',
            fddbrxm: '',
            current: 1,
            size: 10
        },
        pageData: {
            records: [],
            total: 0,
            current: 1,
            size: 10,
            pages: 0
        },

        // 初始化方法
        init() {
            console.log('纳税人查询系统初始化...');
            this.loadCachedData();
            // 只有在有缓存的查询条件时才自动查询
            if (this.hasSearchConditions()) {
                this.queryData();
            }
        },

        // 加载缓存数据
        loadCachedData() {
            try {
                const cachedForm = localStorage.getItem(CACHE_KEYS.SEARCH_FORM);
                if (cachedForm) {
                    this.searchForm = { ...this.searchForm, ...JSON.parse(cachedForm) };
                }

                const cachedPage = localStorage.getItem(CACHE_KEYS.CURRENT_PAGE);
                if (cachedPage) {
                    this.searchForm.current = parseInt(cachedPage);
                }

                const cachedSize = localStorage.getItem(CACHE_KEYS.PAGE_SIZE);
                if (cachedSize) {
                    this.searchForm.size = parseInt(cachedSize);
                }
            } catch (error) {
                console.warn('加载缓存数据失败:', error);
            }
        },

        // 保存缓存数据
        saveCachedData() {
            try {
                localStorage.setItem(CACHE_KEYS.SEARCH_FORM, JSON.stringify(this.searchForm));
                localStorage.setItem(CACHE_KEYS.CURRENT_PAGE, this.searchForm.current.toString());
                localStorage.setItem(CACHE_KEYS.PAGE_SIZE, this.searchForm.size.toString());
            } catch (error) {
                console.warn('保存缓存数据失败:', error);
            }
        },

        // 检查是否有查询条件
        hasSearchConditions() {
            return !!(
                this.searchForm.keyword ||
                this.searchForm.nsrsbh ||
                this.searchForm.nsrmc ||
                this.searchForm.nsrxz ||
                this.searchForm.swjgMc ||
                this.searchForm.fddbrxm
            );
        },

        // 查询数据
        async queryData() {
            if (this.loading) return;

            // 检查是否有查询条件
            if (!this.hasSearchConditions()) {
                this.showMessage('请输入查询条件后再进行查询', 'info');
                return;
            }

            this.loading = true;
            try {
                const response = await fetch(`${API_BASE}/page`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.searchForm)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    this.pageData = result.data;
                    this.saveCachedData();
                    console.log('查询成功，共', this.pageData.total, '条记录');
                } else {
                    throw new Error(result.message || '查询失败');
                }
            } catch (error) {
                console.error('查询失败:', error);
                this.showMessage('查询失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 重置表单
        resetForm() {
            this.searchForm = {
                keyword: '',
                nsrsbh: '',
                nsrmc: '',
                nsrxz: '',
                swjgMc: '',
                fddbrxm: '',
                current: 1,
                size: 10
            };
            this.pageData = {
                records: [],
                total: 0,
                current: 1,
                size: 10,
                pages: 0
            };
            // 清除所有缓存
            localStorage.removeItem(CACHE_KEYS.SEARCH_FORM);
            localStorage.removeItem(CACHE_KEYS.CURRENT_PAGE);
            localStorage.removeItem(CACHE_KEYS.PAGE_SIZE);
            this.showMessage('表单已重置', 'success');
        },

        // 切换页码
        changePage(page) {
            if (page < 1 || page > this.pageData.pages || page === this.searchForm.current) {
                return;
            }

            // 检查是否有查询条件
            if (!this.hasSearchConditions()) {
                this.showMessage('请输入查询条件后再进行查询', 'info');
                return;
            }

            this.searchForm.current = page;
            this.queryDataWithoutConditionCheck();
        },

        // 查询数据（不检查查询条件，用于分页切换）
        async queryDataWithoutConditionCheck() {
            if (this.loading) return;

            this.loading = true;
            try {
                const response = await fetch(`${API_BASE}/page`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.searchForm)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    this.pageData = result.data;
                    this.saveCachedData();
                    console.log('查询成功，共', this.pageData.total, '条记录');
                } else {
                    throw new Error(result.message || '查询失败');
                }
            } catch (error) {
                console.error('查询失败:', error);
                this.showMessage('查询失败: ' + error.message, 'error');
            } finally {
                this.loading = false;
            }
        },

        // 查看详情
        async viewDetail(nsrsbh) {
            if (!nsrsbh) return;

            try {
                const response = await fetch(`${API_BASE}/detail/${nsrsbh}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.success) {
                    this.showDetailModal(result.data);
                } else {
                    throw new Error(result.message || '获取详情失败');
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                this.showMessage('获取详情失败: ' + error.message, 'error');
            }
        },

        // 显示详情模态框
        showDetailModal(data) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="glass-card max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="title-secondary">纳税人详细信息</h3>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div><strong>纳税人识别号:</strong> ${data.nsrsbh || '-'}</div>
                            <div><strong>纳税人名称:</strong> ${data.nsrmc || '-'}</div>
                            <div><strong>登记序号:</strong> ${data.djxh || '-'}</div>
                            <div><strong>纳税人性质:</strong> ${data.nsrxz || '-'}</div>
                            <div><strong>税务机关:</strong> ${data.swjgMc || '-'}</div>
                            <div><strong>登记时间:</strong> ${data.djsj || '-'}</div>
                            <div><strong>法定代表人:</strong> ${data.fddbrxm || '-'}</div>
                            <div><strong>法人电话:</strong> ${data.fddbrdhhm || '-'}</div>
                            <div><strong>财务负责人:</strong> ${data.cwfzrxm || '-'}</div>
                            <div><strong>财务负责人电话:</strong> ${data.cwfzrdhhm || '-'}</div>
                            <div><strong>办税人:</strong> ${data.bsrxm || '-'}</div>
                            <div><strong>办税人电话:</strong> ${data.bsrdhhm || '-'}</div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        },

        // 导出数据
        async exportData() {
            try {
                const response = await fetch(`${API_BASE}/export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.searchForm)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `纳税人信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showMessage('导出成功', 'success');
            } catch (error) {
                console.error('导出失败:', error);
                this.showMessage('导出失败: ' + error.message, 'error');
            }
        },

        // 显示消息提示
        showMessage(message, type = 'info') {
            const colors = {
                success: 'bg-green-100 text-green-800 border-green-200',
                error: 'bg-red-100 text-red-800 border-red-200',
                info: 'bg-blue-100 text-blue-800 border-blue-200'
            };

            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg border ${colors[type]} z-50 fade-in`;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    };
}
